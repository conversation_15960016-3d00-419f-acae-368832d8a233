"""
VoxDiscover Backend - Main FastAPI Application
Voice AI Agent Platform with Daily.co, Pipecat, and Supabase
"""

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import uvicorn
import logging

from backend.core.config import settings
from backend.core.database import init_db
from backend.api.v1 import agents, sessions, analytics, calls, rtvi, test_agent
from backend.core.logging_config import setup_logging
from fastapi import Depends
from backend.core.dependencies import get_current_user
from backend.api.v1.calls import CallRequest

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)



@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting VoxDiscover Backend...")
    await init_db()
    logger.info("Database initialized")

    yield

    # Shutdown
    logger.info("Shutting down VoxDiscover Backend...")

# Create FastAPI app
app = FastAPI(
    title="VoxDiscover API",
    description="Voice AI Agent Platform - VAPI-like system with Daily.co, Pipecat, and Supabase",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
    lifespan=lifespan
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "voxdiscover-backend",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }



# Include API routers
app.include_router(
    agents.router,
    prefix="/api/v1/agents",
    tags=["agents"]
)

app.include_router(
    sessions.router,
    prefix="/api/v1/sessions",
    tags=["sessions"]
)

app.include_router(
    analytics.router,
    prefix="/api/v1/analytics",
    tags=["analytics"]
)

app.include_router(
    calls.router,
    prefix="/api/v1/calls",
    tags=["calls"]
)

app.include_router(
    rtvi.router,
    prefix="/api/v1/rtvi",
    tags=["rtvi"]
)

app.include_router(
    test_agent.router,
    prefix="/api/v1/test",
    tags=["test"]
)

# RTVI Connect endpoint (root level for RTVI compatibility)
@app.post("/connect")
async def rtvi_connect_root(
    request: CallRequest,
    current_user: dict = Depends(get_current_user)
):
    """RTVI connect endpoint at root level for compatibility"""
    # Delegate to the calls router
    from backend.api.v1.calls import rtvi_connect
    return await rtvi_connect(request, current_user)

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "VoxDiscover Voice AI Agent Platform",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    uvicorn.run(
        "backend.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.ENVIRONMENT == "development",
        log_level="info"
    )
