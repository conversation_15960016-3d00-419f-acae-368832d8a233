"""
RTVI Server Service for VoxDiscover
Implements the RTVI framework for real-time voice interactions
"""

import asyncio
import os
import subprocess
import uuid
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from fastapi import HTTPException
from pipecat.transports.services.helpers.daily_rest import DailyRE<PERSON><PERSON>elper, DailyRoomParams

from backend.core.config import settings
from backend.services.daily_service import DailyService

logger = logging.getLogger(__name__)

class RTVIServerService:
    """RTVI Server Service for managing voice agent sessions"""

    def __init__(self):
        self.daily_service = DailyService()
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.bot_processes: Dict[str, subprocess.Popen] = {}

    async def create_rtvi_session(
        self,
        agent_config: Dict[str, Any],
        user_id: str,
        session_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a new RTVI session with agent configuration"""

        session_id = str(uuid.uuid4())
        logger.info(f"🚀 Creating RTVI session {session_id} for user {user_id}")

        try:
            # Create Daily room for the session
            room_name = f"rtvi-session-{session_id[:8]}"
            room_data = await self.daily_service.create_room(
                room_name=room_name,
                expiry_minutes=agent_config.get('max_session_duration_minutes', 60),
                max_participants=2
            )

            if not room_data:
                raise HTTPException(status_code=500, detail="Failed to create Daily room")

            room_url = room_data["url"]

            # Generate token for user
            token = await self.daily_service.create_token(
                room_name=room_name,
                user_name=f"user-{user_id[:8]}",
                is_owner=False,
                expiry_minutes=agent_config.get('max_session_duration_minutes', 60)
            )

            if not token:
                raise HTTPException(status_code=500, detail="Failed to create access token")

            # Store session information
            session_data = {
                "session_id": session_id,
                "user_id": user_id,
                "agent_config": agent_config,
                "room_url": room_url,
                "room_name": room_name,
                "token": token,
                "status": "created",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "bot_process_id": None
            }

            self.active_sessions[session_id] = session_data

            logger.info(f"✅ RTVI session {session_id} created successfully")
            logger.info(f"🔗 Room URL: {room_url}")

            return {
                "session_id": session_id,
                "room_url": room_url,
                "token": token,
                "status": "created"
            }

        except Exception as e:
            logger.error(f"❌ Failed to create RTVI session: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")

    async def start_rtvi_bot(self, session_id: str) -> Dict[str, Any]:
        """Start the RTVI bot for a session"""

        if session_id not in self.active_sessions:
            raise HTTPException(status_code=404, detail="Session not found")

        session = self.active_sessions[session_id]
        agent_config = session["agent_config"]

        logger.info(f"🤖 Starting RTVI bot for session {session_id}")

        try:
            # Generate bot script based on agent configuration
            bot_script = self._generate_rtvi_bot_script(agent_config, session)

            # Write script to temporary file
            script_path = f"/tmp/rtvi_bot_{session_id}.py"
            with open(script_path, 'w') as f:
                f.write(bot_script)

            # Start bot process
            command = f"python3 {script_path} -u {session['room_url']} -t {session['token']}"
            logger.info(f"🚀 Starting bot process: {command}")

            env = os.environ.copy()
            process = subprocess.Popen(
                [command],
                shell=True,
                bufsize=1,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
            )

            # Store process and update session
            self.bot_processes[session_id] = process
            session["bot_process_id"] = process.pid
            session["status"] = "bot_starting"

            logger.info(f"✅ Bot process started with PID: {process.pid}")

            # Start monitoring the process
            asyncio.create_task(self._monitor_bot_process(session_id, process))

            return {
                "session_id": session_id,
                "bot_process_id": process.pid,
                "status": "bot_starting"
            }

        except Exception as e:
            logger.error(f"❌ Failed to start bot for session {session_id}: {e}")
            session["status"] = "error"
            raise HTTPException(status_code=500, detail=f"Failed to start bot: {str(e)}")

    async def connect_rtvi_client(self, session_id: str) -> Dict[str, Any]:
        """Handle RTVI client connection request"""

        if session_id not in self.active_sessions:
            raise HTTPException(status_code=404, detail="Session not found")

        session = self.active_sessions[session_id]

        # Start bot if not already started
        if not session.get("bot_process_id"):
            await self.start_rtvi_bot(session_id)

        # Return connection credentials for RTVI client
        return {
            "room_url": session["room_url"],
            "token": session["token"],
            "session_id": session_id
        }

    async def end_rtvi_session(self, session_id: str) -> Dict[str, Any]:
        """End an RTVI session and cleanup resources"""

        if session_id not in self.active_sessions:
            raise HTTPException(status_code=404, detail="Session not found")

        session = self.active_sessions[session_id]
        logger.info(f"🔚 Ending RTVI session {session_id}")

        try:
            # Terminate bot process if running
            if session_id in self.bot_processes:
                process = self.bot_processes[session_id]
                if process.poll() is None:
                    logger.info(f"🛑 Terminating bot process {process.pid}")
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()

                del self.bot_processes[session_id]

            # Delete Daily room
            room_name = session["room_name"]
            await self.daily_service.delete_room(room_name)

            # Update session status
            session["status"] = "ended"
            session["ended_at"] = datetime.now(timezone.utc).isoformat()

            logger.info(f"✅ RTVI session {session_id} ended successfully")

            return {
                "session_id": session_id,
                "status": "ended"
            }

        except Exception as e:
            logger.error(f"❌ Failed to end session {session_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to end session: {str(e)}")

    async def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """Get the status of an RTVI session"""

        if session_id not in self.active_sessions:
            raise HTTPException(status_code=404, detail="Session not found")

        session = self.active_sessions[session_id]

        # Check bot process status
        if session_id in self.bot_processes:
            process = self.bot_processes[session_id]
            bot_status = "running" if process.poll() is None else "stopped"
        else:
            bot_status = "not_started"

        return {
            "session_id": session_id,
            "status": session["status"],
            "bot_status": bot_status,
            "room_url": session["room_url"],
            "created_at": session["created_at"],
            "agent_name": session["agent_config"].get("name", "Unknown")
        }

    async def _monitor_bot_process(self, session_id: str, process: subprocess.Popen):
        """Monitor bot process output and status"""

        try:
            logger.info(f"🔍 Monitoring bot process for session {session_id}")

            while process.poll() is None:
                # Read stdout and stderr
                if process.stdout and process.stdout.readable():
                    try:
                        line = process.stdout.readline()
                        if line:
                            logger.info(f"🤖 [Session {session_id}] STDOUT: {line.decode().strip()}")
                    except:
                        pass

                if process.stderr and process.stderr.readable():
                    try:
                        line = process.stderr.readline()
                        if line:
                            logger.error(f"🤖 [Session {session_id}] STDERR: {line.decode().strip()}")
                    except:
                        pass

                await asyncio.sleep(0.1)

            # Process ended
            return_code = process.returncode
            if return_code == 0:
                logger.info(f"✅ Bot process for session {session_id} ended successfully")
            else:
                logger.error(f"❌ Bot process for session {session_id} ended with error code: {return_code}")

            # Update session status
            if session_id in self.active_sessions:
                self.active_sessions[session_id]["status"] = "bot_stopped"

        except Exception as e:
            logger.error(f"❌ Error monitoring bot process for session {session_id}: {e}")

    def _generate_rtvi_bot_script(self, agent_config: Dict[str, Any], session: Dict[str, Any]) -> str:
        """Generate RTVI bot script based on agent configuration"""

        # Extract configuration
        agent_name = agent_config.get('name', 'VoxDiscover Agent')
        system_prompt = agent_config.get('system_prompt', 'You are a helpful AI assistant.')
        llm_model = agent_config.get('llm_model', 'gpt-4o-mini')
        llm_temperature = agent_config.get('llm_temperature', 0.7)
        tts_voice_id = agent_config.get('tts_voice_id', 'pNInz6obpgDQGcFmaJgB')
        interruption_sensitivity = agent_config.get('interruption_sensitivity', 0.5)

        return f'''
import asyncio
import os
import sys
import argparse
import aiohttp
from loguru import logger

from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask, PipelineParams
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.processors.frameworks.rtvi import RTVIConfig, RTVIObserver, RTVIProcessor
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.services.elevenlabs.tts import ElevenLabsTTSService
from pipecat.transports.services.daily import DailyTransport, DailyParams

logger.remove(0)
logger.add(sys.stderr, level="DEBUG")

async def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("-u", "--url", required=True, help="Room URL")
    parser.add_argument("-t", "--token", required=True, help="Token")
    args = parser.parse_args()

    async with aiohttp.ClientSession() as session:
        # Set up Daily transport
        transport = DailyTransport(
            args.url,
            args.token,
            "{agent_name}",
            DailyParams(
                audio_in_enabled=True,
                audio_out_enabled=True,
                video_out_enabled=False,
                vad_analyzer=SileroVADAnalyzer(),
                transcription_enabled=True,
            ),
        )

        # Configure LLM
        llm = OpenAILLMService(
            api_key=os.getenv("OPENAI_API_KEY"),
            model="{llm_model}",
            temperature={llm_temperature}
        )

        # Configure TTS
        tts = ElevenLabsTTSService(
            api_key=os.getenv("ELEVENLABS_API_KEY"),
            voice_id="{tts_voice_id}"
        )

        # Set up conversation context
        messages = [
            {{
                "role": "system",
                "content": """{system_prompt} Your output will be converted to audio so don't include special characters in your answers. Keep responses brief and clear."""
            }}
        ]

        context = OpenAILLMContext(messages)
        context_aggregator = llm.create_context_aggregator(context)

        # RTVI processor for client communication
        rtvi = RTVIProcessor(config=RTVIConfig(config=[]))

        # Build pipeline
        pipeline = Pipeline([
            transport.input(),
            rtvi,
            context_aggregator.user(),
            llm,
            tts,
            transport.output(),
            context_aggregator.assistant(),
        ])

        task = PipelineTask(
            pipeline,
            params=PipelineParams(
                allow_interruptions=True,
                enable_metrics=True,
                enable_usage_metrics=True,
            ),
            observers=[RTVIObserver(rtvi)],
        )

        @rtvi.event_handler("on_client_ready")
        async def on_client_ready(rtvi):
            logger.info("🎉 RTVI client ready")
            await rtvi.set_bot_ready()
            # Start conversation
            await task.queue_frames([context_aggregator.user().get_context_frame()])

        @transport.event_handler("on_first_participant_joined")
        async def on_first_participant_joined(transport, participant):
            logger.info(f"👤 Participant joined: {{participant}}")
            await transport.capture_participant_transcription(participant["id"])

        @transport.event_handler("on_participant_left")
        async def on_participant_left(transport, participant, reason):
            logger.info(f"👋 Participant left: {{participant}}")
            await task.cancel()

        runner = PipelineRunner()
        await runner.run(task)

if __name__ == "__main__":
    asyncio.run(main())
'''

# Global RTVI server instance
rtvi_server = RTVIServerService()
