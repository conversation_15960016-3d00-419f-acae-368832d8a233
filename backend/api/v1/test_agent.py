"""
Simple test agent for Daily.co audio testing
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import uuid
import logging
import subprocess
import tempfile
import os

from backend.core.dependencies import get_current_user
from backend.services.daily_service import DailyService

router = APIRouter(tags=["test"])
logger = logging.getLogger(__name__)

@router.post("/create-test-call")
async def create_test_call(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a simple test call with a basic bot that says hello"""
    try:
        logger.info(f"🧪 Creating test call for user {current_user['id']}")

        # Create Daily room
        daily_service = DailyService()
        room_name = f"test-call-{uuid.uuid4().hex[:8]}"

        room_data = await daily_service.create_room(
            room_name=room_name,
            expiry_minutes=30,
            max_participants=2
        )

        # Create token for user
        token = await daily_service.create_token(
            room_name=room_name,
            user_name=f"user-{current_user['id'][:8]}",
            expiry_minutes=30
        )

        # Create simple bot script
        bot_script = _create_simple_bot_script()

        # Write bot script to temp file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(bot_script)
            bot_script_path = f.name

        # Create bot token
        bot_token = await daily_service.create_token(
            room_name=room_name,
            user_name="TestBot",
            expiry_minutes=30
        )

        # Start bot process
        bot_command = [
            "python3", bot_script_path,
            "-u", room_data["url"],
            "-t", bot_token
        ]

        logger.info(f"🤖 Starting simple bot: {' '.join(bot_command)}")

        process = subprocess.Popen(
            bot_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )

        logger.info(f"✅ Simple bot started with PID: {process.pid}")

        return {
            "room_url": room_data["url"],
            "token": token,
            "room_name": room_name,
            "bot_pid": process.pid,
            "message": "Simple test call created. Bot will say hello when you join."
        }

    except Exception as e:
        logger.error(f"❌ Failed to create test call: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create test call: {str(e)}")

def _create_simple_bot_script() -> str:
    """Create a very simple bot script that just says hello"""
    return '''
import asyncio
import os
import sys
import argparse
from loguru import logger

from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask, PipelineParams
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.processors.frame_processor import FrameDirection, FrameProcessor
from pipecat.frames.frames import Frame, TranscriptionFrame
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.services.elevenlabs.tts import ElevenLabsTTSService
from pipecat.services.deepgram.stt import DeepgramSTTService
from pipecat.transports.services.daily import DailyTransport, DailyParams

logger.remove(0)
logger.add(sys.stderr, level="DEBUG")

class STTLogger(FrameProcessor):
    """Log STT transcription events"""

    async def process_frame(self, frame: Frame, direction: FrameDirection):
        if isinstance(frame, TranscriptionFrame):
            logger.info(f"🎤 STT Transcription: '{frame.text}' (final: {frame.final})")

        await self.push_frame(frame, direction)

async def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("-u", "--url", required=True, help="Room URL")
    parser.add_argument("-t", "--token", required=True, help="Token")
    args = parser.parse_args()

    logger.info(f"🤖 Simple test bot starting...")
    logger.info(f"🔗 Room URL: {args.url}")
    logger.info(f"🎫 Token: {args.token[:20]}...")

    # Set up Daily transport with audio enabled
    transport = DailyTransport(
        args.url,
        args.token,
        "TestBot",
        DailyParams(
            audio_in_enabled=True,
            audio_out_enabled=True,
            video_out_enabled=False,
            vad_analyzer=SileroVADAnalyzer(),
            transcription_enabled=False,  # We'll use Deepgram instead
        ),
    )

    # Configure Deepgram STT
    stt = DeepgramSTTService(
        api_key=os.getenv("DEEPGRAM_API_KEY"),
        model="nova-2",
        language="en",
    )

    # Configure LLM
    llm = OpenAILLMService(
        api_key=os.getenv("OPENAI_API_KEY"),
        model="gpt-4o-mini",
        temperature=0.7
    )

    # Configure TTS
    tts = ElevenLabsTTSService(
        api_key=os.getenv("ELEVENLABS_API_KEY"),
        voice_id="pNInz6obpgDQGcFmaJgB"  # Default voice
    )

    # Set up conversation context with simple greeting
    messages = [
        {
            "role": "system",
            "content": "You are a simple test bot. When someone joins, immediately say 'Hello! This is a test bot. Can you hear me clearly? Please say something so I can test if I can hear you too.' When the user speaks, acknowledge what they said and confirm the microphone is working. Keep all responses very short and clear. Your output will be converted to audio so don't use special characters."
        }
    ]

    context = OpenAILLMContext(messages)
    context_aggregator = llm.create_context_aggregator(context)

    # Create STT logger
    stt_logger = STTLogger()

    # Build pipeline with STT and logging
    pipeline = Pipeline([
        transport.input(),
        stt,  # Add Deepgram STT
        stt_logger,  # Log STT output
        context_aggregator.user(),
        llm,
        tts,
        transport.output(),
        context_aggregator.assistant(),
    ])

    task = PipelineTask(
        pipeline,
        params=PipelineParams(
            allow_interruptions=True,
            enable_metrics=True,
            enable_usage_metrics=True,
        ),
    )

    @transport.event_handler("on_first_participant_joined")
    async def on_first_participant_joined(transport, participant):
        logger.info(f"👤 Participant joined: {participant}")
        # Note: We don't capture transcription since we're using Deepgram STT
        # await transport.capture_participant_transcription(participant["id"])
        # Immediately greet the user
        await task.queue_frames([context_aggregator.user().get_context_frame()])

    @transport.event_handler("on_participant_left")
    async def on_participant_left(transport, participant, reason):
        logger.info(f"👋 Participant left: {participant}")
        await task.cancel()

    logger.info("🚀 Starting simple test bot pipeline...")
    runner = PipelineRunner()
    await runner.run(task)

if __name__ == "__main__":
    asyncio.run(main())
'''
