import React, { createContext, useContext, useState, useCallback, useRef } from 'react';
import { RTVIClient } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { RTVIClientProvider } from '@pipecat-ai/client-react';
import { Agent } from '../types';
import { supabase } from '../services/apiClient';

interface RTVIContextType {
  createSession: (agent: Agent) => Promise<RTVIClient | null>;
  endSession: () => Promise<void>;
  currentClient: RTVIClient | null;
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
}

const RTVIContext = createContext<RTVIContextType | null>(null);

export const useRTVI = () => {
  const context = useContext(RTVIContext);
  if (!context) {
    throw new Error('useRTVI must be used within RTVIProvider');
  }
  return context;
};

interface RTVIProviderProps {
  children: React.ReactNode;
}

export const RTVIProvider: React.FC<RTVIProviderProps> = ({ children }) => {
  const [currentClient, setCurrentClient] = useState<RTVIClient | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const transportRef = useRef<DailyTransport | null>(null);

  const createSession = useCallback(async (agent: Agent): Promise<RTVIClient | null> => {
    try {
      setIsConnecting(true);
      setError(null);
      console.log('🚀 Creating RTVI session for agent:', agent.name);

      // Get authentication headers
      console.log('🔐 Getting authentication session...');
      const { data: { session } } = await supabase.auth.getSession();
      console.log('🔐 Session data:', session ? 'Found' : 'Not found');
      if (!session?.access_token) {
        console.error('❌ No authentication token found');
        throw new Error('Not authenticated');
      }
      console.log('✅ Authentication token found, length:', session.access_token.length);

      // Create transport
      const transport = new DailyTransport();
      transportRef.current = transport;

      // Create RTVI client following the demo pattern
      console.log('🤖 Creating RTVI client...');
      const baseUrl = 'http://localhost:8000';
      console.log('🌐 Using base URL:', baseUrl);

      const client = new RTVIClient({
        transport,
        params: {
          baseUrl: baseUrl,
          endpoints: {
            connect: '/api/v1/rtvi/connect',
          },
          requestData: {
            agent_id: agent.id,
            access_token: session.access_token,
          },
        },
        enableMic: true,
        enableCam: false,
      });

      console.log('📡 RTVI client created for agent:', agent.id);

        // Set up event listeners
        client.on('connected', async () => {
          console.log('✅ RTVI client connected');
          setIsConnected(true);
          setIsConnecting(false);

          // Ensure audio is enabled after connection
          try {
            console.log('🔊 Enabling audio output...');
            client.enableMic(true);
            client.enableCam(false); // Disable camera but ensure audio works
            console.log('✅ Audio enabled successfully');
          } catch (err) {
            console.warn('⚠️ Failed to enable audio:', err);
          }
        });

        client.on('disconnected', () => {
          console.log('👋 RTVI client disconnected');
          setIsConnected(false);
          setIsConnecting(false);
        });

        client.on('error', (error: any) => {
          console.error('❌ RTVI client error:', error);
          setError(error.message || 'Connection error');
          setIsConnecting(false);
        });

        client.on('botReady', async () => {
          console.log('🤖 Bot is ready');

          // Try to activate audio context when bot is ready
          try {
            if ('AudioContext' in window) {
              const audioContext = new (window as any).AudioContext();
              if (audioContext.state === 'suspended') {
                console.log('🔊 Resuming suspended audio context...');
                await audioContext.resume();
                console.log('✅ Audio context resumed');
              }
            }
          } catch (err) {
            console.warn('⚠️ Failed to resume audio context:', err);
          }
        });

        client.on('userTranscript', (data: any) => {
          console.log('🎤 User said:', data.text);
        });

        client.on('botTranscript', (data: any) => {
          console.log('🤖 Bot said:', data.text);
        });

        // Add RTVI transport event logging
        client.on('transportStateChanged', (state: any) => {
          console.log('🔄 Transport state changed:', state);
        });

        client.on('participantConnected', (participant: any) => {
          console.log('👤 Participant connected:', participant);
        });

        client.on('participantLeft', (participant: any) => {
          console.log('👋 Participant left:', participant);
        });

        client.on('trackStarted', (track: any) => {
          console.log('🎵 Track started:', track);
          if (track.kind === 'audio') {
            console.log('🔊 Audio track started - should hear sound now');
            console.log('🔊 Track enabled:', track.enabled);
            console.log('🔊 Track muted:', track.muted);
            console.log('🔊 Track label:', track.label);

            // Enable the track if it's disabled
            if (!track.enabled) {
              console.log('🔧 Enabling disabled audio track...');
              track.enabled = true;

              // Force enable after a short delay
              setTimeout(() => {
                if (!track.enabled) {
                  console.log('🔧 Force enabling audio track again...');
                  track.enabled = true;
                }
                console.log('🔊 Track enabled status after fix:', track.enabled);
              }, 100);
            }

            // Also check if this is the bot's audio track
            if (track.label && track.label.includes('-')) {
              console.log('🤖 Bot audio track detected:', track.label);
              // This looks like a bot audio track (UUID format)
              // Make sure it's enabled and not muted
              track.enabled = true;
              if (track.muted) {
                console.log('🔧 Unmuting bot audio track...');
                // Note: We can't directly unmute, but we can log it
              }
            }
          }
        });

        client.on('trackStopped', (track: any) => {
          console.log('🔇 Track stopped:', track);
        });

        // Add audio debugging
        client.on('botTtsStarted', () => {
          console.log('🗣️ Bot TTS started');
        });

        client.on('botTtsStopped', () => {
          console.log('🔇 Bot TTS stopped');
        });

      setCurrentClient(client);

      // Connect using RTVI client
      console.log('🔌 Connecting RTVI client...');

      // Check audio permissions before connecting
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        console.log('🎤 Microphone access granted');
        stream.getTracks().forEach(track => track.stop()); // Clean up
      } catch (err) {
        console.warn('⚠️ Microphone access denied or unavailable:', err);
      }

      // Check audio output capability
      if ('AudioContext' in window || 'webkitAudioContext' in window) {
        console.log('🔊 Web Audio API available');
      } else {
        console.warn('⚠️ Web Audio API not available');
      }

      await client.connect();

      console.log('✅ Successfully connected via RTVI client');

      console.log('🎉 RTVI session created successfully');
      return client;

    } catch (err: any) {
      console.error('❌ Failed to create RTVI session:', err);
      setError(err.message || 'Failed to create session');
      setIsConnecting(false);
      return null;
    }
  }, []);

  const endSession = useCallback(async () => {
    try {
      console.log('🔚 Ending RTVI session');

      if (currentClient) {
        await currentClient.disconnect();
        setCurrentClient(null);
      }

      if (transportRef.current) {
        // Clean up transport if needed
        transportRef.current = null;
      }

      setIsConnected(false);
      setIsConnecting(false);
      setError(null);

      console.log('✅ RTVI session ended');
    } catch (err: any) {
      console.error('❌ Failed to end RTVI session:', err);
      setError(err.message || 'Failed to end session');
    }
  }, [currentClient]);

  const contextValue: RTVIContextType = {
    createSession,
    endSession,
    currentClient,
    isConnected,
    isConnecting,
    error,
  };

  return (
    <RTVIContext.Provider value={contextValue}>
      {currentClient ? (
        <RTVIClientProvider client={currentClient}>
          {children}
        </RTVIClientProvider>
      ) : (
        children
      )}
    </RTVIContext.Provider>
  );
};
