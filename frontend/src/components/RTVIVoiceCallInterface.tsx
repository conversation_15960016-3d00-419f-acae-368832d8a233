import React, { useState, useEffect } from 'react';
import {
  useRTVIClient,
  useRTVIClientTransportState,
  useRTVI<PERSON>lientEvent,
  RTVIClientAudio,
} from '@pipecat-ai/client-react';
import { RTVIEvent, TranscriptData, BotLLMTextData } from '@pipecat-ai/client-js';
import {
  PhoneIcon,
  PhoneXMarkIcon,
  MicrophoneIcon,
  SpeakerWaveIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';
import { Agent } from '../types';
import { useRTVI } from '../providers/RTVIProvider';

interface RTVIVoiceCallInterfaceProps {
  agent: Agent;
  onClose: () => void;
}

interface TranscriptEntry {
  id: string;
  type: 'user' | 'bot';
  text: string;
  timestamp: Date;
  final?: boolean;
}

const RTVIVoiceCallInterface: React.FC<RTVIVoiceCallInterfaceProps> = ({ agent, onClose }) => {
  const { createSession, endSession, isConnected, isConnecting, error } = useRTVI();
  const client = useRTVIClient();
  const transportState = useRTVIClientTransportState();

  const [isStartingCall, setIsStartingCall] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [transcripts, setTranscripts] = useState<TranscriptEntry[]>([]);
  const [showTranscripts, setShowTranscripts] = useState(false);
  const [botReady, setBotReady] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);

  // Handle user transcripts
  useRTVIClientEvent(
    RTVIEvent.UserTranscript,
    React.useCallback((data: TranscriptData) => {
      if (data.final) {
        setTranscripts(prev => [...prev, {
          id: `user-${Date.now()}`,
          type: 'user',
          text: data.text,
          timestamp: new Date(),
          final: true,
        }]);
      }
    }, [])
  );

  // Handle bot transcripts
  useRTVIClientEvent(
    RTVIEvent.BotTranscript,
    React.useCallback((data: BotLLMTextData) => {
      setTranscripts(prev => [...prev, {
        id: `bot-${Date.now()}`,
        type: 'bot',
        text: data.text,
        timestamp: new Date(),
        final: true,
      }]);
    }, [])
  );

  // Handle bot ready state
  useRTVIClientEvent(
    RTVIEvent.BotReady,
    React.useCallback(() => {
      console.log('🤖 Bot is ready for conversation');
      setBotReady(true);
    }, [])
  );

  // Handle transport state changes
  useRTVIClientEvent(
    RTVIEvent.TransportStateChanged,
    React.useCallback((state: string) => {
      console.log('🔄 Transport state changed:', state);
    }, [])
  );

  const startCall = async () => {
    setIsStartingCall(true);
    try {
      console.log('🚀 Starting RTVI call with agent:', agent.name);
      const client = await createSession(agent);
      if (client) {
        console.log('✅ RTVI session created successfully, keeping modal open');
      } else {
        console.error('❌ Failed to create RTVI session - no client returned');
        setLocalError('Failed to create RTVI session');
      }
    } catch (err: any) {
      console.error('❌ Failed to start call:', err);
      setLocalError(err.message || 'Failed to start call');
    } finally {
      setIsStartingCall(false);
    }
  };

  const handleEndCall = async () => {
    console.log('🔚 Ending RTVI call');
    await endSession();
    onClose();
  };

  // Add effect to prevent modal from closing unexpectedly
  useEffect(() => {
    console.log('🔍 RTVIVoiceCallInterface mounted for agent:', agent.name);
    return () => {
      console.log('🔍 RTVIVoiceCallInterface unmounting for agent:', agent.name);
    };
  }, [agent.name]);

  // Monitor connection state changes
  useEffect(() => {
    console.log('🔍 Connection state changed:', { isConnected, isConnecting, error, localError });
  }, [isConnected, isConnecting, error, localError]);

  const toggleMute = async () => {
    if (client) {
      try {
        if (isMuted) {
          client.enableMic(true);
        } else {
          client.enableMic(false);
        }
        setIsMuted(!isMuted);
      } catch (err) {
        console.error('❌ Failed to toggle mute:', err);
      }
    }
  };

  const testAudio = async () => {
    console.log('🔊 Testing browser audio...');
    try {
      // Create a simple beep sound
      const audioContext = new (window as any).AudioContext();

      // Resume audio context if suspended
      if (audioContext.state === 'suspended') {
        console.log('🔊 Resuming audio context...');
        await audioContext.resume();
      }

      console.log('🔊 Audio context state:', audioContext.state);

      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = 800;
      oscillator.type = 'sine';
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1.0);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 1.0);

      console.log('✅ Audio test completed - you should hear a 1-second beep');
      console.log('🔊 If you can\'t hear the beep, your browser audio is blocked');
    } catch (err) {
      console.error('❌ Audio test failed:', err);
      console.log('🔊 Try clicking somewhere on the page first to enable audio');
    }
  };

  const debugAudioElements = () => {
    console.log('🔍 Debugging audio elements...');
    const audioElements = document.querySelectorAll('audio');
    const videoElements = document.querySelectorAll('video');

    console.log(`📊 Found ${audioElements.length} audio elements and ${videoElements.length} video elements`);

    audioElements.forEach((audio, index) => {
      console.log(`🔊 Audio ${index}:`, {
        src: audio.src,
        muted: audio.muted,
        volume: audio.volume,
        paused: audio.paused,
        readyState: audio.readyState,
        autoplay: audio.autoplay
      });

      // Try to unmute and play
      if (audio.muted) {
        console.log(`🔧 Unmuting audio ${index}...`);
        audio.muted = false;
      }

      if (audio.volume === 0) {
        console.log(`🔧 Setting volume for audio ${index}...`);
        audio.volume = 1.0;
      }

      if (audio.paused) {
        console.log(`🔧 Attempting to play audio ${index}...`);
        audio.play().catch(err => console.warn(`⚠️ Failed to play audio ${index}:`, err));
      }
    });

    videoElements.forEach((video, index) => {
      console.log(`🎥 Video ${index}:`, {
        src: video.src,
        muted: video.muted,
        volume: video.volume,
        paused: video.paused,
        readyState: video.readyState,
        autoplay: video.autoplay
      });
    });
  };

  const getStatusText = () => {
    if (isConnecting || isStartingCall) return 'Connecting...';
    if (isConnected && botReady) return `Connected to ${agent.name}`;
    if (isConnected) return 'Connected, waiting for bot...';
    return 'Ready to connect';
  };

  const getStatusColor = () => {
    if (error) return 'text-red-600';
    if (isConnected && botReady) return 'text-green-600';
    if (isConnecting || isStartingCall) return 'text-yellow-600';
    return 'text-gray-600';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[99vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              RTVI Voice Call: {agent.name}
            </h3>
            <p className={`text-sm ${getStatusColor()}`}>
              {getStatusText()}
            </p>
            <p className="text-xs text-gray-400">
              Transport: {transportState}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowTranscripts(!showTranscripts)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              title="Toggle Transcripts"
            >
              <ChatBubbleLeftRightIcon className="h-5 w-5" />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="flex h-128">
          {/* Main Call Interface */}
          <div className={`${showTranscripts ? 'w-2/3' : 'w-full'} p-6`}>
            {(error || localError) && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{error || localError}</p>
              </div>
            )}

            {/* Agent Info */}
            <div className="mb-6 bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Agent Configuration</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">LLM:</span>
                  <span className="ml-2 font-medium">{agent.llm_provider} / {agent.llm_model}</span>
                </div>
                <div>
                  <span className="text-gray-500">STT:</span>
                  <span className="ml-2 font-medium">{agent.stt_provider}</span>
                </div>
                <div>
                  <span className="text-gray-500">TTS:</span>
                  <span className="ml-2 font-medium">{agent.tts_provider}</span>
                </div>
                <div>
                  <span className="text-gray-500">Language:</span>
                  <span className="ml-2 font-medium">{agent.stt_language}</span>
                </div>
              </div>
            </div>

            {/* Call Interface */}
            {isConnected ? (
              <div className="text-center py-8">
                <div className="mb-6">
                  <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <PhoneIcon className="h-10 w-10 text-green-600" />
                  </div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    {botReady ? `Connected to ${agent.name}` : 'Connecting to bot...'}
                  </h4>
                  <p className="text-gray-600">
                    {botReady
                      ? 'Voice call is active. You can speak with the agent.'
                      : 'Waiting for the bot to be ready...'
                    }
                  </p>
                </div>

                {/* Call Controls */}
                <div className="flex justify-center space-x-4">
                  <button
                    onClick={toggleMute}
                    className={`p-3 rounded-full ${
                      isMuted
                        ? 'bg-red-100 text-red-600 hover:bg-red-200'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    } transition-colors`}
                    title={isMuted ? 'Unmute' : 'Mute'}
                  >
                    <MicrophoneIcon className="h-6 w-6" />
                  </button>

                  <button
                    onClick={testAudio}
                    className="p-3 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors"
                    title="Test Audio"
                  >
                    <SpeakerWaveIcon className="h-6 w-6" />
                  </button>

                  <button
                    onClick={handleEndCall}
                    className="p-3 rounded-full bg-red-100 text-red-600 hover:bg-red-200 transition-colors"
                    title="End Call"
                  >
                    <PhoneXMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="mb-6">
                  <div className="mx-auto w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <PhoneIcon className="h-10 w-10 text-blue-600" />
                  </div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    Ready to start RTVI voice call
                  </h4>
                  <p className="text-gray-600">
                    Click the button below to connect with {agent.name} using RTVI
                  </p>
                </div>

                <div className="flex flex-col items-center gap-4">
                  <button
                    onClick={startCall}
                    disabled={isStartingCall || isConnecting}
                    className="flex items-center gap-2 px-6 py-3 text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50"
                  >
                    <PhoneIcon className="h-5 w-5" />
                    {isStartingCall || isConnecting ? 'Connecting...' : 'Start RTVI Call'}
                  </button>

                  <button
                    onClick={testAudio}
                    className="flex items-center gap-2 px-4 py-2 text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 border border-blue-200"
                  >
                    <SpeakerWaveIcon className="h-4 w-4" />
                    Test Audio (Beep)
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Transcripts Panel */}
          {showTranscripts && (
            <div className="w-1/3 border-l border-gray-200 p-4">
              <h4 className="font-medium text-gray-900 mb-4">Live Transcripts</h4>
              <div className="space-y-2 max-h-80 overflow-y-auto">
                {transcripts.map((transcript) => (
                  <div
                    key={transcript.id}
                    className={`p-2 rounded-lg text-sm ${
                      transcript.type === 'user'
                        ? 'bg-blue-50 text-blue-900 ml-4'
                        : 'bg-gray-50 text-gray-900 mr-4'
                    }`}
                  >
                    <div className="font-medium text-xs mb-1">
                      {transcript.type === 'user' ? 'You' : agent.name}
                      <span className="text-gray-500 ml-2">
                        {transcript.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <div>{transcript.text}</div>
                  </div>
                ))}
                {transcripts.length === 0 && (
                  <div className="text-gray-500 text-sm text-center py-8">
                    Transcripts will appear here during the conversation
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* RTVI Audio Component */}
        <RTVIClientAudio />

        {/* Debug: Check for audio elements */}
        {isConnected && (
          <div className="p-2 bg-gray-100 text-xs">
            <div>Audio elements on page: {document.querySelectorAll('audio').length}</div>
            <div>Video elements on page: {document.querySelectorAll('video').length}</div>
            <button
              onClick={debugAudioElements}
              className="mt-2 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
            >
              Debug Audio Elements
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default RTVIVoiceCallInterface;
