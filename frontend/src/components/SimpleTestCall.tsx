import React, { useState, useRef, useEffect } from 'react';
import { PhoneIcon, SpeakerWaveIcon, MicrophoneIcon } from '@heroicons/react/24/outline';
import { apiClient } from '../lib/api';

interface SimpleTestCallProps {
  onClose: () => void;
}

export const SimpleTestCall: React.FC<SimpleTestCallProps> = ({ onClose }) => {
  const [isCreatingCall, setIsCreatingCall] = useState(false);
  const [callData, setCallData] = useState<any>(null);
  const [isJoined, setIsJoined] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const callFrameRef = useRef<any>(null);

  const createTestCall = async () => {
    setIsCreatingCall(true);
    setError(null);
    
    try {
      console.log('🧪 Creating simple test call...');
      const response = await apiClient.post('/test/create-test-call');
      console.log('✅ Test call created:', response.data);
      setCallData(response.data);
    } catch (err: any) {
      console.error('❌ Failed to create test call:', err);
      setError(err.response?.data?.detail || 'Failed to create test call');
    } finally {
      setIsCreatingCall(false);
    }
  };

  const joinCall = async () => {
    if (!callData) return;

    try {
      console.log('🔗 Joining Daily call...');
      
      // Import Daily.co SDK dynamically
      const DailyIframe = (await import('@daily-co/daily-js')).default;
      
      // Create call frame
      const callFrame = DailyIframe.createFrame({
        showLeaveButton: true,
        showFullscreenButton: false,
        showLocalVideo: false,
        showParticipantsBar: false,
      });
      
      callFrameRef.current = callFrame;

      // Set up event listeners
      callFrame.on('joined-meeting', () => {
        console.log('✅ Joined Daily meeting');
        setIsJoined(true);
      });

      callFrame.on('left-meeting', () => {
        console.log('👋 Left Daily meeting');
        setIsJoined(false);
        callFrame.destroy();
        callFrameRef.current = null;
      });

      callFrame.on('participant-joined', (event: any) => {
        console.log('👤 Participant joined:', event.participant);
      });

      callFrame.on('participant-left', (event: any) => {
        console.log('👋 Participant left:', event.participant);
      });

      callFrame.on('error', (event: any) => {
        console.error('❌ Daily error:', event);
        setError(`Daily error: ${event.errorMsg}`);
      });

      // Join the call
      await callFrame.join({
        url: callData.room_url,
        token: callData.token,
        userName: 'Test User',
      });

    } catch (err: any) {
      console.error('❌ Failed to join call:', err);
      setError(`Failed to join call: ${err.message}`);
    }
  };

  const leaveCall = () => {
    if (callFrameRef.current) {
      callFrameRef.current.leave();
    }
  };

  const testAudio = async () => {
    console.log('🔊 Testing browser audio...');
    try {
      const audioContext = new (window as any).AudioContext();
      
      if (audioContext.state === 'suspended') {
        await audioContext.resume();
      }
      
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.value = 800;
      oscillator.type = 'sine';
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1.0);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 1.0);
      
      console.log('✅ Audio test completed');
    } catch (err) {
      console.error('❌ Audio test failed:', err);
    }
  };

  useEffect(() => {
    return () => {
      // Cleanup on unmount
      if (callFrameRef.current) {
        callFrameRef.current.destroy();
      }
    };
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Simple Test Call</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {!callData && (
          <div className="text-center">
            <p className="text-gray-600 mb-6">
              This will create a simple Daily.co call with a basic test bot that says hello.
            </p>
            
            <div className="space-y-4">
              <button
                onClick={testAudio}
                className="flex items-center gap-2 px-4 py-2 text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 border border-blue-200 mx-auto"
              >
                <SpeakerWaveIcon className="h-4 w-4" />
                Test Audio (Beep)
              </button>

              <button
                onClick={createTestCall}
                disabled={isCreatingCall}
                className="flex items-center gap-2 px-6 py-3 text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 mx-auto"
              >
                <PhoneIcon className="h-5 w-5" />
                {isCreatingCall ? 'Creating...' : 'Create Test Call'}
              </button>
            </div>
          </div>
        )}

        {callData && !isJoined && (
          <div className="text-center">
            <p className="text-gray-600 mb-4">
              Test call created! Bot PID: {callData.bot_pid}
            </p>
            <p className="text-sm text-gray-500 mb-6">
              {callData.message}
            </p>
            
            <button
              onClick={joinCall}
              className="flex items-center gap-2 px-6 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 mx-auto"
            >
              <PhoneIcon className="h-5 w-5" />
              Join Test Call
            </button>
          </div>
        )}

        {isJoined && (
          <div className="text-center">
            <p className="text-green-600 mb-4">✅ Connected to test call</p>
            <p className="text-sm text-gray-600 mb-6">
              You should hear the bot say hello. Try speaking to test two-way audio.
            </p>
            
            <button
              onClick={leaveCall}
              className="flex items-center gap-2 px-6 py-3 text-white bg-red-600 rounded-lg hover:bg-red-700 mx-auto"
            >
              <PhoneIcon className="h-5 w-5" />
              Leave Call
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
